use std::path::{Path, PathBuf};
use std::time::Instant;
use anyhow::{Result, anyhow};
use futures::StreamExt;
use tokio::fs::{File, OpenOptions, create_dir_all};
use tokio::io::{AsyncSeekExt, SeekFrom, AsyncWriteExt};
use tracing::{debug, info, error};
use log::warn;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// Initialize the downloader
    pub async fn init(&mut self) -> Result<()> {
        // 获取当前设置
        let settings = self.config_manager.get_settings().await;
        
        // 使用with_buffer_size方法更新缓冲区大小
        let buffer_size = settings.download.buffer_size as usize;
        *self = self.clone().with_buffer_size(buffer_size);
        debug!("Updated buffer size to {} bytes", buffer_size);
        
        // 更新块大小
        if let Some(chunk_size) = settings.download.chunk_size.try_into().ok() {
            self.chunk_size = chunk_size;
            debug!("Updated chunk size to {} bytes", self.chunk_size);
        }
        
        // 使用with_temp_file_extension方法更新临时文件扩展名
        if let Some(extension) = &settings.download.temp_file_extension {
            *self = self.clone().with_temp_file_extension(extension.clone());
            debug!("Updated temp file extension to {}", extension);
        }
        
        // 使用with_flush_interval方法更新刷新间隔
        if let Some(interval_ms) = settings.download.flush_interval_ms {
            let interval = std::time::Duration::from_millis(interval_ms);
            *self = self.clone().with_flush_interval(interval);
            debug!("Updated flush interval to {} ms", interval_ms);
        }
        
        // 创建取消通道 - 使用 broadcast 通道支持多个接收者
        let (cancel_tx, _) = tokio::sync::broadcast::channel::<()>(16);
        self.cancel_sender = Some(cancel_tx);
        
        // 更新HTTP客户端
        self.update_client().await?;
        
        Ok(())
    }

    /// Internal download implementation
    pub(crate) async fn internal_download(&mut self) -> Result<()> {
        // 解析输出路径
        let output_path = if Path::new(&self.output_path).is_absolute() {
            PathBuf::from(&self.output_path)
        } else {
            // 如果是相对路径，则相对于下载目录
            let settings = self.config_manager.get_settings().await;
            let download_dir = Path::new(&settings.download.path);
            download_dir.join(&self.output_path)
        };
        
        debug!("Output path resolved to: {}", output_path.display());
        
        // 获取临时文件路径
        let temp_file_path = self.get_temp_file_path(&output_path.to_string_lossy());
        debug!("Temporary file path: {}", temp_file_path);
        
        // 创建输出目录
        if let Some(parent) = output_path.parent() {
            debug!("Creating output directory: {}", parent.display());
            match create_dir_all(parent).await {
                Ok(_) => debug!("Output directory created successfully"),
                Err(e) => {
                    error!("Failed to create directory: {} - {}", parent.display(), e);
                    return Err(anyhow!("Failed to create directory: {} - {}", parent.display(), e));
                }
            }
        }
        
        // 创建临时文件目录
        let temp_path = Path::new(&temp_file_path);
        if let Some(temp_parent) = temp_path.parent() {
            debug!("Creating temporary file directory: {}", temp_parent.display());
            match create_dir_all(temp_parent).await {
                Ok(_) => debug!("Temporary file directory created successfully"),
                Err(e) => {
                    error!("Failed to create temporary directory: {} - {}", temp_parent.display(), e);
                    return Err(anyhow!("Failed to create temporary directory: {} - {}", temp_parent.display(), e));
                }
            }
        }
        
        // 加载恢复点
        let resume_offset = match self.load_resume_point().await {
            Ok(offset) => {
                debug!("Resume offset: {} bytes", offset);
                offset
            },
            Err(e) => {
                warn!("Failed to load resume point: {}, starting from beginning", e);
                0
            }
        };
        
        // 打开临时文件
        debug!("Opening temporary file: {}", temp_file_path);
        let mut file = match OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .open(&temp_file_path)
            .await {
                Ok(f) => f,
                Err(e) => {
                    error!("Failed to open temporary file: {} - {}", temp_file_path, e);
                    return Err(anyhow!("Failed to open temporary file: {} - {}", temp_file_path, e));
                }
            };
        
        // 如果有恢复点，则定位到恢复点
        if resume_offset > 0 {
            debug!("Seeking to resume point: {} bytes", resume_offset);
            match file.seek(SeekFrom::Start(resume_offset)).await {
                Ok(_) => debug!("Successfully seeked to resume point"),
                Err(e) => {
                    error!("Failed to seek to resume point: {}", e);
                    return Err(anyhow!("Failed to seek to resume point: {}", e));
                }
            };
            self.downloaded_size = resume_offset;
            debug!("Resumed download from {} bytes", resume_offset);
        }
        
        // 初始化速度计算
        self.last_speed_update = Some(Instant::now());
        self.bytes_since_speed_update = 0;
        
        // 初始化缓冲区
        self.buffer = Vec::with_capacity(self.buffer_size);
        
        // 发送HEAD请求获取文件大小和是否支持断点续传
        debug!("Sending HEAD request to get file size and range support");
        let head_response = match self.client.head(&self.url).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HEAD request: {}", e);
                return Err(anyhow!("Failed to send HEAD request: {}", e));
            }
        };
        
        // 检查是否支持断点续传
        self.supports_range = head_response.headers()
            .get(reqwest::header::ACCEPT_RANGES)
            .map(|v| v.to_str().unwrap_or("").contains("bytes"))
            .unwrap_or(false);
        
        // 获取文件大小
        self.total_size = head_response.headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<u64>().ok());
        
        debug!("File size: {:?} bytes, supports range: {}", self.total_size, self.supports_range);
        
        // 如果文件大小为0，则直接创建空文件并返回
        if let Some(size) = self.total_size {
            if size == 0 {
                // 创建空文件
                debug!("File size is 0, creating empty file");
                let mut empty_file = match File::create(&output_path).await {
                    Ok(f) => f,
                    Err(e) => {
                        error!("Failed to create empty file: {} - {}", output_path.display(), e);
                        return Err(anyhow!("Failed to create empty file: {} - {}", output_path.display(), e));
                    }
                };
                
                match empty_file.flush().await {
                    Ok(_) => debug!("Empty file flushed successfully"),
                    Err(e) => {
                        error!("Failed to flush empty file: {}", e);
                        return Err(anyhow!("Failed to flush empty file: {}", e));
                    }
                };
                
                // 删除临时文件
                if Path::new(&temp_file_path).exists() {
                    debug!("Removing temporary file: {}", temp_file_path);
                    match tokio::fs::remove_file(&temp_file_path).await {
                        Ok(_) => debug!("Temporary file removed successfully"),
                        Err(e) => {
                            warn!("Failed to remove temporary file: {} - {}", temp_file_path, e);
                            // 不返回错误，因为这不是致命错误
                        }
                    };
                }
                
                return Ok(());
            }
        }
        
        // 下载文件
        let start_offset = resume_offset;
        debug!("Starting download from offset: {} bytes", start_offset);
        
        // 如果支持范围请求且文件大小已知，使用分块下载
        if self.supports_range && self.total_size.is_some() {
            let total_size = self.total_size.unwrap();
            let mut current_offset = start_offset;
            
            while current_offset < total_size {
                // 检查是否暂停或取消
                if self.is_paused || self.is_cancelled {
                    debug!("Download paused or cancelled during chunked download");
                    return Ok(());
                }
        
                // 计算当前块的结束位置
                let end_offset = std::cmp::min(current_offset + self.chunk_size - 1, total_size - 1);
                debug!("Downloading chunk from {} to {} bytes", current_offset, end_offset);
                
                let mut stream = match self.download_chunk(current_offset, Some(end_offset)).await {
                    Ok(s) => s,
                    Err(e) => {
                        error!("Failed to download chunk {}-{}: {}", current_offset, end_offset, e);
                        return Err(anyhow!("Failed to download chunk: {}", e));
                    }
                };
        
                // 处理当前块的数据
                while let Some(chunk_result) = stream.next().await {
                    // 检查是否暂停或取消
                    if self.is_paused || self.is_cancelled {
                        debug!("Download paused or cancelled during chunked download");
                        // 刷新缓冲区
                        self.flush_buffer(&mut file).await?;

                        // 如果暂停，保存恢复点
                        if self.is_paused {
                            self.save_resume_point().await.unwrap_or_else(|e| {
                                warn!("Failed to save resume point: {}", e);
                            });
                        }

                        // 如果取消，清理临时文件
                        if self.is_cancelled {
                            drop(file);
                            if Path::new(&temp_file_path).exists() {
                                let _ = tokio::fs::remove_file(&temp_file_path).await;
                            }
                            if let Some(resume_manager) = &self.resume_manager {
                                let _ = resume_manager.delete_resume_point(self.task_id).await;
                            }
                        }

                        return Ok(());
                    }

                    // 处理块数据
                    match chunk_result {
                        Ok(chunk) => {
                            let chunk_size = chunk.len() as u64;

                            // 应用带宽限制
                            if let Some(scheduler) = &self.bandwidth_scheduler {
                                match scheduler.wait_for_download_quota(self.task_id, chunk_size as usize).await {
                                    Ok(_) => {},
                                    Err(e) => {
                                        warn!("Failed to apply bandwidth limit: {}", e);
                                        // 继续下载，不中断
                                    }
                                };
                            }

                            // 写入缓冲区
                            match self.write_to_buffer(&chunk, &mut file).await {
                                Ok(_) => {},
                                Err(e) => {
                                    error!("Failed to write data to buffer: {}", e);
                                    return Err(anyhow!("Failed to write data to buffer: {}", e));
                                }
                            };

                            // 更新下载统计
                            self.downloaded_size += chunk_size;
                            self.bytes_since_speed_update += chunk_size;

                            // 更新下载速度
                            self.update_speed();

                            // 每下载1MB保存一次恢复点
                            if self.downloaded_size % (1024 * 1024) < chunk_size {
                                match self.save_resume_point().await {
                                    Ok(_) => {},
                                    Err(e) => {
                                        warn!("Failed to save periodic resume point: {}", e);
                                        // 不中断下载
                                    }
                                };
                            }

                            // 打印进度
                            if self.total_size.is_some() {
                                let total_size = self.total_size.unwrap();
                                if self.downloaded_size % (5 * 1024 * 1024) < chunk_size {
                                    let progress = (self.downloaded_size as f64 / total_size as f64) * 100.0;
                                    debug!("Download progress: {:.2}% ({}/{} bytes)",
                                          progress, self.downloaded_size, total_size);
                                }
                            } else {
                                // 如果文件大小未知，只显示已下载的字节数
                                if self.downloaded_size % (5 * 1024 * 1024) < chunk_size {
                                    debug!("Downloaded {} bytes", self.downloaded_size);
                                }
                            }
                        },
                        Err(e) => {
                            error!("Error downloading chunk: {}", e);
                            return Err(anyhow!("Failed to download chunk: {}", e));
                        }
                    }
                }
                
                // 更新偏移量，准备下载下一个块
                current_offset = end_offset + 1;
            }

            // 分块下载完成，刷新缓冲区并完成文件处理
            self.flush_buffer(&mut file).await?;
            file.flush().await?;
            drop(file);

            // 重命名临时文件为最终文件
            debug!("Renaming temporary file {} to final file {}",
                   temp_file_path, output_path.display());

            // 检查目标文件是否已存在，如果存在则先删除
            if output_path.exists() {
                debug!("Target file already exists, removing it first");
                tokio::fs::remove_file(&output_path).await?;
            }

            // 执行重命名操作
            tokio::fs::rename(&temp_file_path, &output_path).await?;

            // 删除恢复点
            if let Some(resume_manager) = &self.resume_manager {
                debug!("Removing resume point for completed download");
                resume_manager.delete_resume_point(self.task_id).await.unwrap_or_else(|e| {
                    warn!("Failed to delete resume point: {}", e);
                });
            }

            info!("Download completed: {}", output_path.display());
            Ok(())
        } else {
            // 不支持范围请求或文件大小未知，使用单一请求下载
            let mut stream = match self.download_chunk(start_offset, None).await {
                Ok(s) => s,
                Err(e) => {
                    error!("Failed to start download chunk: {}", e);
                    return Err(anyhow!("Failed to start download chunk: {}", e));
                }
            };
        
            // 处理下载块
            while let Some(_chunk_result) = stream.next().await {
                // 检查是否暂停或取消
                if self.is_paused || self.is_cancelled {
                    debug!("Download {} - task paused: {}, cancelled: {}", 
                          self.task_id, self.is_paused, self.is_cancelled);
                    
                    // 刷新缓冲区
                    match self.flush_buffer(&mut file).await {
                        Ok(_) => debug!("Buffer flushed successfully"),
                        Err(e) => {
                            error!("Failed to flush buffer: {}", e);
                            return Err(anyhow!("Failed to flush buffer: {}", e));
                        }
                    };
            
                    // 保存恢复点
                    if self.is_paused {
                        debug!("Saving resume point for paused download");
                        match self.save_resume_point().await {
                            Ok(_) => debug!("Resume point saved successfully"),
                            Err(e) => {
                                warn!("Failed to save resume point: {}", e);
                                // 不返回错误，因为这不是致命错误
                            }
                        };
                    }
                    
                    // 如果取消，则删除临时文件
                    if self.is_cancelled {
                        debug!("Cleaning up cancelled download");
                        match file.flush().await {
                            Ok(_) => debug!("File flushed successfully"),
                            Err(e) => {
                                warn!("Failed to flush file before closing: {}", e);
                                // 不返回错误，继续清理
                            }
                        };
                        drop(file); // 关闭文件
                        
                        // 删除临时文件
                        if Path::new(&temp_file_path).exists() {
                            debug!("Removing temporary file for cancelled download: {}", temp_file_path);
                            match tokio::fs::remove_file(&temp_file_path).await {
                                Ok(_) => debug!("Temporary file removed successfully"),
                                Err(e) => {
                                    warn!("Failed to remove temporary file: {} - {}", temp_file_path, e);
                                    // 不返回错误，因为这不是致命错误
                                }
                            };
                        }
                        
                        // 删除恢复点
                        if let Some(resume_manager) = &self.resume_manager {
                            debug!("Removing resume point for cancelled download");
                            match resume_manager.delete_resume_point(self.task_id).await {
                                Ok(_) => debug!("Resume point deleted successfully"),
                                Err(e) => {
                                    warn!("Failed to delete resume point: {}", e);
                                    // 不返回错误，因为这不是致命错误
                                }
                            };
                        }
                    }
                    
                    return Ok(());
        }
        
                // 刷新缓冲区
                debug!("Download completed, flushing final buffer");
                match self.flush_buffer(&mut file).await {
                    Ok(_) => debug!("Final buffer flush successful"),
                    Err(e) => {
                        error!("Failed to flush final buffer: {}", e);
                        return Err(anyhow!("Failed to flush final buffer: {}", e));
                    }
                };
                
                // 关闭文件
                match file.flush().await {
                    Ok(_) => debug!("File flushed successfully before closing"),
                    Err(e) => {
                        error!("Failed to flush file before closing: {}", e);
                        return Err(anyhow!("Failed to flush file before closing: {}", e));
                    }
                };
                drop(file);
        
                // 重命名临时文件为最终文件
                debug!("Renaming temporary file {} to final file {}", 
                       temp_file_path, output_path.display());
                
                // 检查目标文件是否已存在，如果存在则先删除
                if Path::new(&output_path).exists() {
                    debug!("Target file already exists, removing it first");
                    match tokio::fs::remove_file(&output_path).await {
                        Ok(_) => debug!("Existing target file removed successfully"),
                        Err(e) => {
                            error!("Failed to remove existing target file: {} - {}", output_path.display(), e);
                            return Err(anyhow!("Failed to remove existing target file: {} - {}", output_path.display(), e));
                        }
                    };
                }
                
                // 执行重命名操作
                match tokio::fs::rename(&temp_file_path, &output_path).await {
                    Ok(_) => debug!("File renamed successfully"),
                    Err(e) => {
                        error!("Failed to rename temporary file: {} - {}", temp_file_path, e);
                        return Err(anyhow!("Failed to rename temporary file {} to {}: {}", 
                                  temp_file_path, output_path.display(), e));
                    }
                };
                
                // 删除恢复点
                if let Some(resume_manager) = &self.resume_manager {
                    debug!("Removing resume point for completed download");
                    match resume_manager.delete_resume_point(self.task_id).await {
                        Ok(_) => debug!("Resume point deleted successfully"),
                        Err(e) => {
                            warn!("Failed to delete resume point: {}", e);
                            // 不返回错误，因为这不是致命错误
                        }
                    };
                }
                
                info!("Download completed: {}", output_path.display());
                
                return Ok(());
            }
            
            // 添加返回值
            Ok(())
        }
    }
}