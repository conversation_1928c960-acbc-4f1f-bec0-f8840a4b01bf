use std::any::Any;
use async_trait::async_trait;
use uuid::Uuid;
use tracing::{error, debug, info};

use crate::core::CoreError;

use crate::core::interfaces::Downloader;
use crate::core::interfaces::downloader::{ProtocolType, DownloadStatus, DownloadProgress};
use crate::core::interfaces::DownloadOptions;
use crate::core::error::CoreResult;

use super::downloader::HttpDownloader;

#[async_trait]
impl Downloader for HttpDownloader {
    fn protocol_type(&self) -> ProtocolType {
        ProtocolType::Http
    }
    
    fn supports_url(&self, url: &str) -> bool {
        url.starts_with("http://") || url.starts_with("https://")
    }
    
    async fn init(&mut self, url: &str, output_path: &str, _options: &DownloadOptions) -> CoreResult<()> {
        tracing::info!("Initializing HTTP download for {}", url);

        // 保存URL和输出路径
        self.url = url.to_string();
        self.output_path = output_path.to_string();
        
        // 初始化HTTP下载器
        self.init().await.map_err(Into::into)
    }

    async fn start(&mut self) -> CoreResult<()> {
        // 使用内部下载方法
        self.internal_download().await.map_err(Into::into)
    }

    async fn pause(&mut self) -> CoreResult<()> {
        // 直接实现暂停逻辑
        if self.is_paused {
            return Ok(());
        }
        
        self.is_paused = true;
        
        // 保存恢复点
        if let Some(resume_manager) = &self.resume_manager {
            resume_manager.pause_task(&self.task_id).await.map_err(Into::<CoreError>::into)?
        }
        
        Ok(())
    }

    async fn resume(&mut self) -> CoreResult<()> {
        // 检查是否支持范围请求，这将影响恢复策略
        let supports_range = self.supports_range_requests();
        
        if !supports_range {
            info!("HTTP server does not support range requests for {}, resuming may restart the download", self.url);
        } else {
            debug!("HTTP server supports range requests for {}, can resume from {} bytes", 
                  self.url, self.downloaded_size);
        }
        
        // 直接实现恢复逻辑
        if !self.is_paused {
            debug!("Task {} is not paused, cannot resume.", self.task_id);
            return Ok(());
        }
        
        debug!("Resuming download for task {}", self.task_id);
        self.is_paused = false;
        
        // 恢复任务
        if let Some(resume_manager) = &self.resume_manager {
            if let Err(e) = resume_manager.resume_task(&self.task_id).await {
                error!("Failed to resume task {} in resume manager: {}", self.task_id, e);
                return Err(anyhow::anyhow!("Failed to resume task in resume manager: {}", e).into());
            }
        }
        
        // 重新启动下载流程
        let task_id = self.task_id;
        tokio::spawn({
            let mut this = self.clone();
            async move {
                debug!("Starting new download task for resumed task {}", task_id);
                if let Err(e) = this.internal_download().await {
                    error!("Failed to resume download for task {}: {}", task_id, e);
                }
            }
        });
        
        Ok(())
    }

    async fn cancel(&mut self) -> CoreResult<()> {
        // 直接实现取消逻辑
        if self.is_cancelled {
            return Ok(());
        }
        
        self.is_cancelled = true;
        
        // 如果存在取消通道，发送取消信号
        if let Some(cancel_sender) = &self.cancel_sender {
            // 尝试发送取消信号，忽略可能的错误（接收端可能已关闭）
            let _ = cancel_sender.send(()).await;
            debug!("Cancel signal sent for task {}", self.task_id);
        }
        
        // 删除恢复点
        if let Some(resume_manager) = &self.resume_manager {
            resume_manager.delete_resume_point(self.task_id).await.map_err(Into::<CoreError>::into)?
        }
        
        Ok(())
    }

    async fn progress(&self) -> CoreResult<DownloadProgress> {
        let progress_percentage = match self.total_size {
            Some(total) if total > 0 => (self.downloaded_size as f64 / total as f64) * 100.0,
            _ => 0.0,
        };
        
        Ok(DownloadProgress {
            total_size: self.total_size,
            downloaded_size: self.downloaded_size,
            progress_percentage,
            speed: self.current_speed,
            eta: None, // TODO: Calculate ETA
        })
    }

    async fn status(&self) -> CoreResult<DownloadStatus> {
        let status = if self.is_cancelled {
            DownloadStatus::Cancelled
        } else if self.is_paused {
            DownloadStatus::Paused
        } else if self.downloaded_size > 0 {
            DownloadStatus::Downloading
        } else {
            DownloadStatus::Initializing
        };
        
        Ok(status)
    }

    fn id(&self) -> Uuid {
        self.task_id
    }

    fn url(&self) -> &str {
        &self.url
    }

    fn output_path(&self) -> &str {
        &self.output_path
    }
    
    fn clone_box(&self) -> Box<dyn Downloader> {
        Box::new(self.clone())
    }
    
    async fn speed(&self) -> CoreResult<u64> {
        Ok(self.current_speed)
    }
    
    async fn get_total_size(&self) -> CoreResult<Option<u64>> {
        Ok(self.total_size)
    }
    
    async fn get_downloaded_size(&self) -> CoreResult<u64> {
        Ok(self.downloaded_size)
    }
    
    fn as_any(&self) -> &dyn Any {
        self
    }
    
    async fn set_download_limit(&mut self, bytes_per_second: Option<u64>) -> CoreResult<()> {
        self.set_download_limit(bytes_per_second).await;
        Ok(())
    }
    
    async fn set_upload_limit(&mut self, bytes_per_second: Option<u64>) -> CoreResult<()> {
        self.set_upload_limit(bytes_per_second).await;
        Ok(())
    }
}