use std::sync::Arc;
use std::time::{Duration, Instant};
use uuid::Uuid;
use tokio::sync::mpsc;

use crate::config::ConfigManager;
use crate::utils::{HttpClient, HttpClientTrait};
use crate::download::resume::{ResumeManager, ResumePoint};
use crate::download::bandwidth_scheduler::BandwidthScheduler;

/// HTTP download protocol implementation
#[derive(Clone)]
pub struct HttpDownloader {
    pub(crate) url: String,
    pub(crate) output_path: String,
    pub(crate) client: Arc<dyn HttpClientTrait>,
    pub(crate) config_manager: Arc<ConfigManager>,
    pub(crate) task_id: Uuid,
    pub(crate) total_size: Option<u64>,
    pub(crate) downloaded_size: u64,
    pub(crate) supports_range: bool,
    pub(crate) is_paused: bool,
    pub(crate) is_cancelled: bool,
    // 断点续传相关字段
    pub(crate) resume_manager: Option<Arc<dyn ResumeManager>>,
    // 带宽调度相关字段
    pub(crate) bandwidth_scheduler: Option<Arc<dyn BandwidthScheduler>>,
    pub(crate) chunk_size: u64,
    pub(crate) last_speed_update: Option<Instant>,
    pub(crate) current_speed: u64,
    pub(crate) bytes_since_speed_update: u64,
    pub(crate) resume_point: Option<ResumePoint>,
    // 缓冲区相关字段
    pub(crate) buffer: Vec<u8>,
    pub(crate) buffer_size: usize,
    // 临时文件相关字段
    pub(crate) temp_file_extension: String,
    pub(crate) last_flush_time: Option<Instant>,
    pub(crate) flush_interval: Duration,
    // 取消请求相关字段
    pub(crate) cancel_sender: Option<tokio::sync::broadcast::Sender<()>>,
}

impl HttpDownloader {
    /// Create a new HTTP downloader
    pub fn new(url: String, output_path: String, config_manager: Arc<ConfigManager>, task_id: Uuid) -> Self {
        // 使用默认设置，避免在构造函数中使用block_on
        // 客户端和设置将在init方法中正确初始化
        let client = Arc::new(HttpClient::new().expect("Failed to create HTTP client"));
        let chunk_size = 1024 * 1024; // 默认1MB，将在init中更新
        
        // 默认缓冲区大小为8MB
        let buffer_size = 8 * 1024 * 1024;
        
        Self {
            url,
            output_path,
            client,
            config_manager,
            task_id,
            total_size: None,
            downloaded_size: 0,
            supports_range: false,
            is_paused: false,
            is_cancelled: false,
            resume_manager: None,
            bandwidth_scheduler: None,
            chunk_size,
            last_speed_update: None,
            current_speed: 0,
            bytes_since_speed_update: 0,
            resume_point: None,
            // 初始化缓冲区相关字段
            buffer: Vec::with_capacity(buffer_size),
            buffer_size,
            // 初始化临时文件相关字段
            temp_file_extension: ".tmp".to_string(),
            last_flush_time: None,
            flush_interval: Duration::from_secs(1), // 默认1秒刷新一次
            // 初始化取消通道
            cancel_sender: None,
        }
    }

    /// Set the resume manager
    pub fn with_resume_manager(mut self, resume_manager: Arc<dyn ResumeManager>) -> Self {
        self.resume_manager = Some(resume_manager);
        self
    }

    /// Set the bandwidth scheduler
    pub fn with_bandwidth_scheduler(mut self, bandwidth_scheduler: Arc<dyn BandwidthScheduler>) -> Self {
        self.bandwidth_scheduler = Some(bandwidth_scheduler);
        self
    }

    /// Set the http client
    pub fn with_http_client(mut self, client: Arc<dyn HttpClientTrait>) -> Self {
        self.client = client;
        self
    }
    
    /// 设置缓冲区大小
    pub fn with_buffer_size(mut self, buffer_size: usize) -> Self {
        self.buffer_size = buffer_size;
        self.buffer = Vec::with_capacity(buffer_size);
        self
    }
    
    /// 设置临时文件扩展名
    pub fn with_temp_file_extension(mut self, extension: String) -> Self {
        self.temp_file_extension = extension;
        self
    }
    
    /// 设置刷新间隔
    pub fn with_flush_interval(mut self, interval: Duration) -> Self {
        self.flush_interval = interval;
        self
    }
    
    /// Update the HTTP client based on current configuration
    pub async fn update_client(&mut self) -> anyhow::Result<()> {
        // 获取当前设置
        let settings = self.config_manager.get_settings().await;
        
        // 创建新的HTTP客户端
        let client = Arc::new(HttpClient::from_settings(&settings)?); 
        
        // 更新客户端
        self.client = client;
        
        Ok(())
    }

    /// Check if the downloader supports range requests
    pub fn supports_range_requests(&self) -> bool {
        self.supports_range
    }

    // 设置下载速度限制
    pub async fn set_download_limit(&mut self, bytes_per_second: Option<u64>) {
        if let Some(scheduler) = &self.bandwidth_scheduler {
            if let Err(e) = scheduler.set_task_download_limit(self.task_id, bytes_per_second).await {
                tracing::error!("Failed to set download limit for task {}: {}", self.task_id, e);
            }
        }
    }

    // 设置上传速度限制
    pub async fn set_upload_limit(&mut self, bytes_per_second: Option<u64>) {
        if let Some(scheduler) = &self.bandwidth_scheduler {
            if let Err(e) = scheduler.set_task_upload_limit(self.task_id, bytes_per_second).await {
                tracing::error!("Failed to set upload limit for task {}: {}", self.task_id, e);
            }
        }
    }
}