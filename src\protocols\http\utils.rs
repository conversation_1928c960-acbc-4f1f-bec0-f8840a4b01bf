use std::path::Path;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use bytes::Bytes;
use futures::Stream;
use futures::StreamExt;
use tokio::io::AsyncWriteExt;
use tracing::{debug, error};

use crate::download::manager::TaskInfo;
use crate::download::manager::TaskStatus;
use crate::core::interfaces::downloader::Downloader;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 获取临时文件路径
    pub fn get_temp_file_path(&self, output_path: &str) -> String {
        // 使用Path来正确处理路径
        let path = Path::new(output_path);
        let file_stem = path.file_stem().unwrap_or_default();
        let extension = path.extension().unwrap_or_default();
        
        // 构建新的文件名：原文件名+临时扩展名
        let mut temp_filename = file_stem.to_string_lossy().to_string();
        if !extension.is_empty() {
            temp_filename.push_str(".");
            temp_filename.push_str(&extension.to_string_lossy());
        }
        temp_filename.push_str(&self.temp_file_extension);
        
        // 将新文件名与父目录路径结合
        if let Some(parent) = path.parent() {
            // 确保使用相同的目录，不创建新的临时目录
            return parent.join(temp_filename).to_string_lossy().to_string();
        }
        
        // 如果没有父目录，直接返回文件名
        temp_filename
    }
    
    /// 刷新缓冲区到文件
    pub(crate) async fn flush_buffer(&mut self, file: &mut tokio::fs::File) -> Result<()> {
        if !self.buffer.is_empty() {
            debug!("Flushing {} bytes from buffer to file", self.buffer.len());
            file.write_all(&self.buffer).await?;
            file.flush().await?;
            self.buffer.clear();
            self.last_flush_time = Some(Instant::now());
        }
        Ok(())
    }
    
    /// 写入数据到缓冲区，如果缓冲区满或达到刷新间隔则刷新到文件
    pub(crate) async fn write_to_buffer(&mut self, data: &[u8], file: &mut tokio::fs::File) -> Result<()> {
        // 如果数据加上当前缓冲区大小超过了缓冲区容量，先刷新缓冲区
        if self.buffer.len() + data.len() > self.buffer_size {
            self.flush_buffer(file).await?
        }
        
        // 如果单个数据块大于缓冲区容量，直接写入文件
        if data.len() > self.buffer_size {
            debug!("Data chunk size ({}) exceeds buffer size, writing directly to file", data.len());
            file.write_all(data).await?;
            file.flush().await?;
            self.last_flush_time = Some(Instant::now());
        } else {
            // 否则添加到缓冲区
            self.buffer.extend_from_slice(data);
            
            // 检查是否需要定期刷新
            let should_flush = if let Some(last_flush) = self.last_flush_time {
                Instant::now().duration_since(last_flush) >= self.flush_interval
            } else {
                true
            };
            
            if should_flush {
                self.flush_buffer(file).await?
            }
        }
        
        Ok(())
    }

    /// Download a chunk of the file
    pub(crate) async fn download_chunk(&self, start: u64, end: Option<u64>) -> Result<impl Stream<Item = Result<Bytes, reqwest::Error>>> {
        // 构建请求头
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::ACCEPT, "*/*".parse().unwrap());
        
        // 使用chunk_size来确定下载块的大小
        let end = match end {
            Some(end) => Some(end),
            None => {
                if self.supports_range {
                    // 如果支持范围请求，使用chunk_size来限制请求大小
                    Some(start + self.chunk_size - 1)
                } else {
                    None
                }
            }
        };
        
        if self.supports_range {
            let range = match end {
                Some(end) => format!("bytes={}-{}", start, end),
                None => format!("bytes={}-", start),
            };
            headers.insert(reqwest::header::RANGE, range.parse().unwrap());
            debug!("Setting range header: {}", range);
        }
    
        // 打印请求信息
        debug!("Sending request to {}", self.url);
        
        // 创建取消通道的接收端（用于支持下载中断/取消）
        // 推荐做法：在调用 download_chunk 前，通过可变引用设置好 self.cancel_sender（broadcast::Sender），
        // 并在此处 subscribe 一个 receiver，这样外部可以安全地广播取消信号。
        // 取消时外部调用 sender.send(())，所有 receiver 都会收到信号。
        let mut cancel_rx = match &self.cancel_sender {
            Some(sender) => {
                // 规范做法：broadcast subscribe
                sender.subscribe()
            },
            None => {
                // 兜底：没有 sender，返回一个不会收到信号的 receiver
                let (dummy_sender, dummy_rx) = tokio::sync::broadcast::channel::<()>(1);
                dummy_rx
            }
        };
    
        // 使用HttpClientTrait的get_with_headers方法，而不是inner().get()
        let response = match self.client.get_with_headers(&self.url, headers).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HTTP request: {}", e);
                return Err(anyhow!("Failed to send HTTP request: {}", e));
            }
        };
    
        // 打印响应信息
        let status = response.status();
        debug!("Received response: status={}", status);
    
        if !status.is_success() {
            // 尝试获取响应体内容以提供更详细的错误信息
            let error_body = match response.text().await {
                Ok(body) => {
                    if body.len() > 1000 {
                        format!("{}..... (truncated)", &body[..1000])
                    } else {
                        body
                    }
                },
                Err(_) => "<unable to read response body>".to_string(),
            };
            
            error!("HTTP request failed with status {}: {}", status, error_body);
            return Err(anyhow!("Failed to download chunk: HTTP {} - {}", status, error_body));
        }
        
        // 获取响应流
        let stream = response.bytes_stream();
        
        // 创建一个可以被取消的流
        let stream = futures::stream::unfold(
            (stream, cancel_rx),
            |(mut stream, mut cancel_rx)| Box::pin(async move {
                tokio::select! {
                    // 尝试从流中获取下一个数据块
                    chunk = stream.next() => {
                        if let Some(chunk) = chunk {
                            Some((chunk, (stream, cancel_rx)))
                        } else {
                            None
                        }
                    },
                    // 如果收到取消信号，则中断流
                    _ = cancel_rx.recv() => {
                        debug!("Download cancelled via cancel channel");
                        None
                    },
                }
            }),
        );
    
        Ok(stream)
    }

    /// 加载恢复点
    pub(crate) async fn load_resume_point(&mut self) -> Result<u64> {
        if let Some(resume_manager) = &self.resume_manager {
            // 尝试加载恢复点
            if let Some(resume_point) = resume_manager.load_resume_point(self.task_id).await? {
                // 检查URL是否匹配
                if resume_point.url == self.url {
                    // 保存恢复点信息
                    self.resume_point = Some(resume_point.clone());
                    
                    // 检查恢复点中的路径是否是临时文件路径
                    // 如果是临时文件路径，则使用它
                    // 如果不是，则检查是否存在对应的临时文件
                    let resume_path = &resume_point.output_path;
                    if resume_path.ends_with(&self.temp_file_extension) {
                        debug!("Found resume point with temporary file path: {}", resume_path);
                        // 已经是临时文件路径，直接返回
                        return Ok(resume_point.downloaded_size);
                    } else {
                        // 不是临时文件路径，检查是否存在对应的临时文件
                        let temp_path = self.get_temp_file_path(resume_path);
                        if Path::new(&temp_path).exists() {
                            debug!("Found temporary file for resume: {}", temp_path);
                            return Ok(resume_point.downloaded_size);
                        }
                    }
                    
                    return Ok(resume_point.downloaded_size);
                }
            }
        }

        Ok(0)
    }

    /// 保存恢复点
    pub(crate) async fn save_resume_point(&self) -> Result<()> {
        if let Some(resume_manager) = &self.resume_manager {
            // 获取实际输出路径
            let output_path = if Path::new(&self.output_path).is_absolute() {
                self.output_path.clone()
            } else {
                // 如果是相对路径，则相对于下载目录
                let settings = self.config_manager.get_settings().await;
                let download_dir = Path::new(&settings.download.path);
                let full_path = download_dir.join(&self.output_path);
                full_path.to_string_lossy().to_string()
            };
            
            // 保存临时文件路径而不是最终文件路径
            let temp_file_path = self.get_temp_file_path(&output_path);
            
            // 创建任务信息
            let task_info = TaskInfo {
                id: self.task_id,
                url: self.url.clone(),
                output_path: temp_file_path, // 保存临时文件路径
                status: TaskStatus::Paused,
                progress: self.progress().await?.progress_percentage,
                speed: self.current_speed,
                total_size: self.total_size,
                downloaded_size: self.downloaded_size,
                uploaded_bytes: 0, // HTTP 下载协议通常不上传数据，初始化为0
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                error_message: None,
            };

            // 保存恢复点
            resume_manager.save_resume_point(&task_info).await?
        }

        Ok(())
    }

    /// 更新下载速度
    pub(crate) fn update_speed(&mut self) {
        if let Some(last_update) = self.last_speed_update {
            let now = Instant::now();
            let elapsed = now.duration_since(last_update);

            // 每秒更新一次速度
            if elapsed >= Duration::from_secs(1) {
                let bytes_per_second = (self.bytes_since_speed_update as f64 / elapsed.as_secs_f64()) as u64;
                self.current_speed = bytes_per_second;
                self.last_speed_update = Some(now);
                self.bytes_since_speed_update = 0;
            }
        }
    }
}