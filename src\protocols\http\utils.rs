use std::path::Path;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use bytes::Bytes;
use futures::Stream;
use futures::StreamExt;
use tokio::io::AsyncWriteExt;
use tracing::{debug, error};

use crate::download::manager::TaskInfo;
use crate::download::manager::TaskStatus;
use crate::core::interfaces::downloader::Downloader;

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 获取临时文件路径
    pub fn get_temp_file_path(&self, output_path: &str) -> String {
        // 使用Path来正确处理路径
        let path = Path::new(output_path);
        let file_stem = path.file_stem().unwrap_or_default();
        let extension = path.extension().unwrap_or_default();
        
        // 构建新的文件名：原文件名+临时扩展名
        let mut temp_filename = file_stem.to_string_lossy().to_string();
        if !extension.is_empty() {
            temp_filename.push_str(".");
            temp_filename.push_str(&extension.to_string_lossy());
        }
        temp_filename.push_str(&self.temp_file_extension);
        
        // 将新文件名与父目录路径结合
        if let Some(parent) = path.parent() {
            // 确保使用相同的目录，不创建新的临时目录
            return parent.join(temp_filename).to_string_lossy().to_string();
        }
        
        // 如果没有父目录，直接返回文件名
        temp_filename
    }
    
    /// 刷新缓冲区到文件
    pub(crate) async fn flush_buffer(&mut self, file: &mut tokio::fs::File) -> Result<()> {
        if !self.buffer.is_empty() {
            debug!("Flushing {} bytes from buffer to file", self.buffer.len());
            file.write_all(&self.buffer).await?;
            file.flush().await?;
            self.buffer.clear();
            self.last_flush_time = Some(Instant::now());
        }
        Ok(())
    }
    
    /// 写入数据到缓冲区，如果缓冲区满或达到刷新间隔则刷新到文件
    pub(crate) async fn write_to_buffer(&mut self, data: &[u8], file: &mut tokio::fs::File) -> Result<()> {
        // 如果数据加上当前缓冲区大小超过了缓冲区容量，先刷新缓冲区
        if self.buffer.len() + data.len() > self.buffer_size {
            self.flush_buffer(file).await?
        }
        
        // 如果单个数据块大于缓冲区容量，直接写入文件
        if data.len() > self.buffer_size {
            debug!("Data chunk size ({}) exceeds buffer size, writing directly to file", data.len());
            file.write_all(data).await?;
            file.flush().await?;
            self.last_flush_time = Some(Instant::now());
        } else {
            // 否则添加到缓冲区
            self.buffer.extend_from_slice(data);
            
            // 检查是否需要定期刷新
            let should_flush = if let Some(last_flush) = self.last_flush_time {
                Instant::now().duration_since(last_flush) >= self.flush_interval
            } else {
                true
            };
            
            if should_flush {
                self.flush_buffer(file).await?
            }
        }
        
        Ok(())
    }

    /// Download a chunk of the file
    pub(crate) async fn download_chunk(&self, start: u64, end: Option<u64>) -> Result<impl Stream<Item = Result<Bytes, reqwest::Error>>> {
        // 构建请求头
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::ACCEPT, "*/*".parse().unwrap());
        
        // 使用chunk_size来确定下载块的大小
        let end = match end {
            Some(end) => Some(end),
            None => {
                if self.supports_range {
                    // 如果支持范围请求，使用chunk_size来限制请求大小
                    Some(start + self.chunk_size - 1)
                } else {
                    None
                }
            }
        };
        
        if self.supports_range {
            let range = match end {
                Some(end) => format!("bytes={}-{}", start, end),
                None => format!("bytes={}-", start),
            };
            headers.insert(reqwest::header::RANGE, range.parse().unwrap());
            debug!("Setting range header: {}", range);
        }
    
        // 打印请求信息
        debug!("Sending request to {}", self.url);
        
        // 创建取消通道的接收端（用于支持下载中断/取消）
        // 使用 broadcast 通道支持多个接收者，外部可以安全地广播取消信号
        let mut cancel_rx = match &self.cancel_sender {
            Some(sender) => {
                // 订阅取消信号
                sender.subscribe()
            },
            None => {
                // 兜底：没有 sender，返回一个不会收到信号的 receiver
                let (_, dummy_rx) = tokio::sync::broadcast::channel::<()>(1);
                dummy_rx
            }
        };
    
        // 使用HttpClientTrait的get_with_headers方法，而不是inner().get()
        let response = match self.client.get_with_headers(&self.url, headers).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HTTP request: {}", e);
                return Err(anyhow!("Failed to send HTTP request: {}", e));
            }
        };
    
        // 打印响应信息
        let status = response.status();
        debug!("Received response: status={}", status);
    
        if !status.is_success() {
            // 尝试获取响应体内容以提供更详细的错误信息
            let error_body = match response.text().await {
                Ok(body) => {
                    if body.len() > 1000 {
                        format!("{}..... (truncated)", &body[..1000])
                    } else {
                        body
                    }
                },
                Err(_) => "<unable to read response body>".to_string(),
            };
            
            error!("HTTP request failed with status {}: {}", status, error_body);
            return Err(anyhow!("Failed to download chunk: HTTP {} - {}", status, error_body));
        }
        
        // 获取响应流
        let stream = response.bytes_stream();
        
        // 创建一个可以被取消的流
        let stream = futures::stream::unfold(
            (stream, cancel_rx),
            |(mut stream, mut cancel_rx)| Box::pin(async move {
                tokio::select! {
                    // 尝试从流中获取下一个数据块
                    chunk = stream.next() => {
                        if let Some(chunk) = chunk {
                            Some((chunk, (stream, cancel_rx)))
                        } else {
                            None
                        }
                    },
                    // 如果收到取消信号，则中断流
                    _ = cancel_rx.recv() => {
                        debug!("Download cancelled via cancel channel");
                        None
                    },
                }
            }),
        );
    
        Ok(stream)
    }

    /// 加载恢复点
    pub(crate) async fn load_resume_point(&mut self) -> Result<u64> {
        if let Some(resume_manager) = &self.resume_manager {
            // 尝试加载恢复点
            if let Some(resume_point) = resume_manager.load_resume_point(self.task_id).await? {
                // 检查URL是否匹配
                if resume_point.url == self.url {
                    debug!("Found resume point for URL: {}", self.url);

                    // 确定临时文件路径
                    let temp_file_path = if resume_point.output_path.ends_with(&self.temp_file_extension) {
                        // 恢复点中已经是临时文件路径
                        resume_point.output_path.clone()
                    } else {
                        // 恢复点中是最终文件路径，需要转换为临时文件路径
                        self.get_temp_file_path(&resume_point.output_path)
                    };

                    // 验证临时文件是否存在
                    let temp_path = Path::new(&temp_file_path);
                    if !temp_path.exists() {
                        debug!("Temporary file does not exist: {}, starting from beginning", temp_file_path);
                        return Ok(0);
                    }

                    // 验证临时文件大小是否与恢复点记录一致
                    match tokio::fs::metadata(&temp_path).await {
                        Ok(metadata) => {
                            let file_size = metadata.len();
                            if file_size != resume_point.downloaded_size {
                                debug!("File size mismatch: expected {}, actual {}, starting from beginning",
                                      resume_point.downloaded_size, file_size);
                                // 文件大小不匹配，可能文件已损坏，从头开始下载
                                return Ok(0);
                            }

                            // 如果有总大小信息，检查是否已经完成下载
                            if let Some(total_size) = resume_point.total_size {
                                if file_size >= total_size {
                                    debug!("File appears to be complete, size: {}", file_size);
                                    // 文件已完成，但可能没有重命名，这里返回完整大小
                                    return Ok(file_size);
                                }
                            }

                            debug!("Resume point validated: {} bytes downloaded", file_size);

                            // 保存恢复点信息
                            self.resume_point = Some(resume_point.clone());

                            return Ok(file_size);
                        },
                        Err(e) => {
                            debug!("Failed to get file metadata for {}: {}, starting from beginning", temp_file_path, e);
                            return Ok(0);
                        }
                    }
                } else {
                    debug!("URL mismatch in resume point: expected {}, found {}", self.url, resume_point.url);
                }
            } else {
                debug!("No resume point found for task: {}", self.task_id);
            }
        }

        Ok(0)
    }

    /// 保存恢复点
    pub(crate) async fn save_resume_point(&self) -> Result<()> {
        if let Some(resume_manager) = &self.resume_manager {
            // 获取实际输出路径
            let output_path = if Path::new(&self.output_path).is_absolute() {
                self.output_path.clone()
            } else {
                // 如果是相对路径，则相对于下载目录
                let settings = self.config_manager.get_settings().await;
                let download_dir = Path::new(&settings.download.path);
                let full_path = download_dir.join(&self.output_path);
                full_path.to_string_lossy().to_string()
            };
            
            // 保存临时文件路径而不是最终文件路径
            let temp_file_path = self.get_temp_file_path(&output_path);
            
            // 创建任务信息
            let task_info = TaskInfo {
                id: self.task_id,
                url: self.url.clone(),
                output_path: temp_file_path, // 保存临时文件路径
                status: TaskStatus::Paused,
                progress: self.progress().await?.progress_percentage,
                speed: self.current_speed,
                total_size: self.total_size,
                downloaded_size: self.downloaded_size,
                uploaded_bytes: 0, // HTTP 下载协议通常不上传数据，初始化为0
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
                error_message: None,
            };

            // 保存恢复点
            resume_manager.save_resume_point(&task_info).await?
        }

        Ok(())
    }

    /// 更新下载速度
    pub(crate) fn update_speed(&mut self) {
        if let Some(last_update) = self.last_speed_update {
            let now = Instant::now();
            let elapsed = now.duration_since(last_update);

            // 每秒更新一次速度
            if elapsed >= Duration::from_secs(1) {
                let bytes_per_second = (self.bytes_since_speed_update as f64 / elapsed.as_secs_f64()) as u64;
                self.current_speed = bytes_per_second;
                self.last_speed_update = Some(now);
                self.bytes_since_speed_update = 0;
            }
        }
    }

    /// 创建可取消的下载流
    pub(crate) fn create_cancellable_stream<S, T, E>(
        &self,
        stream: S,
    ) -> impl Stream<Item = Result<T, E>>
    where
        S: Stream<Item = Result<T, E>> + Unpin,
        T: 'static,
        E: 'static + From<tokio::sync::broadcast::error::RecvError>,
    {
        let mut cancel_rx = match &self.cancel_sender {
            Some(sender) => sender.subscribe(),
            None => {
                let (_, rx) = tokio::sync::broadcast::channel::<()>(1);
                rx
            }
        };

        futures::stream::unfold(
            (stream, cancel_rx),
            |(mut stream, mut cancel_rx)| Box::pin(async move {
                tokio::select! {
                    // 尝试从流中获取下一个数据块
                    item = stream.next() => {
                        if let Some(item) = item {
                            Some((item, (stream, cancel_rx)))
                        } else {
                            None
                        }
                    },
                    // 如果收到取消信号，则中断流
                    _ = cancel_rx.recv() => {
                        debug!("Stream cancelled via cancel channel");
                        None
                    },
                }
            }),
        )
    }

    /// 处理取消清理工作
    pub(crate) async fn handle_cancellation_cleanup(&self, temp_file_path: &str) -> Result<()> {
        debug!("Handling cancellation cleanup for task {}", self.task_id);

        // 删除临时文件
        if Path::new(temp_file_path).exists() {
            debug!("Removing temporary file: {}", temp_file_path);
            match tokio::fs::remove_file(temp_file_path).await {
                Ok(_) => debug!("Temporary file removed successfully"),
                Err(e) => {
                    error!("Failed to remove temporary file {}: {}", temp_file_path, e);
                    // 不返回错误，因为这不是致命错误
                }
            }
        }

        // 删除恢复点
        if let Some(resume_manager) = &self.resume_manager {
            debug!("Removing resume point for cancelled download");
            match resume_manager.delete_resume_point(self.task_id).await {
                Ok(_) => debug!("Resume point deleted successfully"),
                Err(e) => {
                    error!("Failed to delete resume point: {}", e);
                    // 不返回错误，因为这不是致命错误
                }
            }
        }

        Ok(())
    }

    /// 处理暂停保存工作
    pub(crate) async fn handle_pause_save(&self) -> Result<()> {
        debug!("Handling pause save for task {}", self.task_id);

        // 保存恢复点
        match self.save_resume_point().await {
            Ok(_) => debug!("Resume point saved successfully"),
            Err(e) => {
                error!("Failed to save resume point: {}", e);
                return Err(e);
            }
        }

        Ok(())
    }

    /// 验证恢复点完整性
    pub(crate) async fn verify_resume_point_integrity(&self, temp_file_path: &str) -> Result<bool> {
        debug!("Verifying resume point integrity for task {}", self.task_id);

        // 检查是否有恢复管理器
        let resume_manager = match &self.resume_manager {
            Some(rm) => rm,
            None => {
                debug!("No resume manager available");
                return Ok(false);
            }
        };

        // 检查临时文件是否存在
        let temp_path = Path::new(temp_file_path);
        if !temp_path.exists() {
            debug!("Temporary file does not exist: {}", temp_file_path);
            return Ok(false);
        }

        // 获取文件大小
        let file_size = match tokio::fs::metadata(temp_path).await {
            Ok(metadata) => metadata.len(),
            Err(e) => {
                debug!("Failed to get file metadata: {}", e);
                return Ok(false);
            }
        };

        // 检查文件大小是否与下载器记录一致
        if file_size != self.downloaded_size {
            debug!("File size mismatch: expected {}, actual {}", self.downloaded_size, file_size);
            return Ok(false);
        }

        // 如果有恢复点信息，进行更详细的验证
        if let Some(resume_point) = &self.resume_point {
            // 验证下载大小
            if resume_point.downloaded_size != file_size {
                debug!("Resume point size mismatch: expected {}, actual {}", resume_point.downloaded_size, file_size);
                return Ok(false);
            }

            // 验证URL
            if resume_point.url != self.url {
                debug!("URL mismatch in resume point");
                return Ok(false);
            }

            // 如果有校验和信息，进行文件完整性验证
            if resume_point.checksum.is_some() && resume_point.checksum_algorithm.is_some() {
                match resume_manager.verify_file_integrity(self.task_id).await {
                    Ok(is_valid) => {
                        if !is_valid {
                            debug!("File integrity verification failed");
                            return Ok(false);
                        }
                    },
                    Err(e) => {
                        debug!("Failed to verify file integrity: {}", e);
                        // 不返回错误，继续下载
                    }
                }
            }
        }

        debug!("Resume point integrity verification passed");
        Ok(true)
    }

    /// 验证服务器是否支持断点续传
    pub(crate) async fn verify_range_support(&mut self) -> Result<bool> {
        debug!("Verifying range support for URL: {}", self.url);

        // 发送HEAD请求检查服务器支持
        let head_response = match self.client.head(&self.url).await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send HEAD request for range support check: {}", e);
                return Err(anyhow!("Failed to send HEAD request: {}", e));
            }
        };

        // 检查Accept-Ranges头
        let supports_range = head_response.headers()
            .get(reqwest::header::ACCEPT_RANGES)
            .map(|v| v.to_str().unwrap_or("").contains("bytes"))
            .unwrap_or(false);

        // 获取文件大小
        let content_length = head_response.headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<u64>().ok());

        // 更新下载器状态
        self.supports_range = supports_range;
        self.total_size = content_length;

        debug!("Range support: {}, Content-Length: {:?}", supports_range, content_length);

        // 如果服务器声称支持范围请求，进行实际测试
        if supports_range && content_length.is_some() {
            let total_size = content_length.unwrap();
            if total_size > 1 {
                // 尝试请求最后一个字节来验证范围支持
                match self.test_range_request(total_size - 1, total_size - 1).await {
                    Ok(actual_supports) => {
                        if !actual_supports {
                            debug!("Server claims to support ranges but test failed");
                            self.supports_range = false;
                        }
                        Ok(self.supports_range)
                    },
                    Err(e) => {
                        debug!("Range support test failed: {}, assuming no range support", e);
                        self.supports_range = false;
                        Ok(false)
                    }
                }
            } else {
                Ok(supports_range)
            }
        } else {
            Ok(supports_range)
        }
    }

    /// 测试范围请求是否真正有效
    async fn test_range_request(&self, start: u64, end: u64) -> Result<bool> {
        debug!("Testing range request: bytes={}-{}", start, end);

        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::RANGE, format!("bytes={}-{}", start, end).parse().unwrap());

        match self.client.get_with_headers(&self.url, headers).await {
            Ok(response) => {
                let status = response.status();
                debug!("Range test response status: {}", status);

                // 206 Partial Content 表示范围请求成功
                if status == reqwest::StatusCode::PARTIAL_CONTENT {
                    // 验证Content-Range头
                    if let Some(content_range) = response.headers().get(reqwest::header::CONTENT_RANGE) {
                        if let Ok(range_str) = content_range.to_str() {
                            debug!("Content-Range: {}", range_str);
                            return Ok(range_str.contains(&format!("{}-{}", start, end)));
                        }
                    }
                    Ok(true)
                } else if status == reqwest::StatusCode::OK {
                    // 服务器返回完整内容，不支持范围请求
                    debug!("Server returned 200 OK instead of 206, range not supported");
                    Ok(false)
                } else {
                    debug!("Range request failed with status: {}", status);
                    Ok(false)
                }
            },
            Err(e) => {
                debug!("Range test request failed: {}", e);
                Ok(false)
            }
        }
    }

    /// 验证恢复下载的一致性
    pub(crate) async fn validate_resume_consistency(&mut self, resume_offset: u64) -> Result<bool> {
        debug!("Validating resume consistency for offset: {}", resume_offset);

        // 如果没有恢复偏移量，无需验证
        if resume_offset == 0 {
            return Ok(true);
        }

        // 检查服务器文件是否发生变化
        if let Some(current_total_size) = self.total_size {
            if let Some(resume_point) = &self.resume_point {
                if let Some(original_total_size) = resume_point.total_size {
                    if current_total_size != original_total_size {
                        debug!("File size changed: original {}, current {}",
                              original_total_size, current_total_size);
                        return Ok(false);
                    }
                }
            }
        }

        // 如果支持范围请求，验证恢复点是否有效
        if self.supports_range && resume_offset > 0 {
            // 尝试从恢复点开始请求一小段数据来验证
            match self.test_resume_point(resume_offset).await {
                Ok(is_valid) => {
                    if !is_valid {
                        debug!("Resume point validation failed");
                        return Ok(false);
                    }
                },
                Err(e) => {
                    debug!("Failed to test resume point: {}", e);
                    return Ok(false);
                }
            }
        }

        debug!("Resume consistency validation passed");
        Ok(true)
    }

    /// 测试恢复点是否有效
    async fn test_resume_point(&self, resume_offset: u64) -> Result<bool> {
        debug!("Testing resume point at offset: {}", resume_offset);

        // 请求从恢复点开始的少量数据（比如1KB）
        let test_size = std::cmp::min(1024, self.chunk_size);
        let end_offset = resume_offset + test_size - 1;

        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(reqwest::header::USER_AGENT, "Tonitru-Downloader/1.0".parse().unwrap());
        headers.insert(reqwest::header::RANGE,
                      format!("bytes={}-{}", resume_offset, end_offset).parse().unwrap());

        match self.client.get_with_headers(&self.url, headers).await {
            Ok(response) => {
                let status = response.status();
                debug!("Resume point test response status: {}", status);

                if status == reqwest::StatusCode::PARTIAL_CONTENT {
                    // 验证Content-Range头
                    if let Some(content_range) = response.headers().get(reqwest::header::CONTENT_RANGE) {
                        if let Ok(range_str) = content_range.to_str() {
                            debug!("Resume test Content-Range: {}", range_str);
                            // 检查范围是否正确
                            return Ok(range_str.contains(&format!("{}-", resume_offset)));
                        }
                    }
                    Ok(true)
                } else {
                    debug!("Resume point test failed with status: {}", status);
                    Ok(false)
                }
            },
            Err(e) => {
                debug!("Resume point test request failed: {}", e);
                Ok(false)
            }
        }
    }
}